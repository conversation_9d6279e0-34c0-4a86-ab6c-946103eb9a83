#!/usr/bin/env python3
"""
PaddleOCR 3.1 Text Recognition Sample Script for macOS
This script demonstrates various ways to use PaddleOCR for text recognition on macOS.

Installation for macOS:
1. pip install paddlepaddle
2. pip install paddleocr

Note: GPU acceleration is disabled for macOS compatibility.
"""

import os
import cv2
import numpy as np
from paddleocr import PaddleOCR
import json
from PIL import Image, ImageDraw, ImageFont

def setup_paddleocr(use_angle_cls=True, lang='en'):
    """
    Initialize PaddleOCR with specified parameters
    
    Args:
        use_angle_cls (bool): Whether to use angle classification
        lang (str): Language code ('en', 'ch', 'fr', 'german', etc.)
        use_gpu (bool): Whether to use GPU acceleration (False for macOS)
    
    Returns:
        PaddleOCR: Initialized OCR object
    """
    # Force GPU to False on macOS for compatibility
    ocr = PaddleOCR(
        use_angle_cls=use_angle_cls,
        lang=lang
    )
    return ocr

def recognize_from_image_path(ocr, image_path):
    """
    Recognize text from an image file path
    
    Args:
        ocr: PaddleOCR instance
        image_path (str): Path to the image file
    
    Returns:
        list: OCR results
    """
    print(f"Processing image: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"Error: Image file not found at {image_path}")
        return None
    
    try:
        result = ocr.ocr(image_path, cls=True)
        return result
    except Exception as e:
        print(f"Error processing image: {e}")
        return None

def recognize_from_numpy_array(ocr, image_array):
    """
    Recognize text from a numpy array (OpenCV image)
    
    Args:
        ocr: PaddleOCR instance
        image_array: numpy array representing the image
    
    Returns:
        list: OCR results
    """
    try:
        result = ocr.ocr(image_array, cls=True)
        return result
    except Exception as e:
        print(f"Error processing image array: {e}")
        return None

def extract_text_only(ocr_result):
    """
    Extract only the text content from OCR results
    
    Args:
        ocr_result: Result from PaddleOCR
    
    Returns:
        list: List of recognized text strings
    """
    if not ocr_result or not ocr_result[0]:
        return []
    
    texts = []
    for line in ocr_result[0]:
        if line:
            text = line[1][0]  # Extract text from [bbox, (text, confidence)]
            texts.append(text)
    
    return texts

def get_detailed_results(ocr_result):
    """
    Extract detailed information including bounding boxes and confidence scores
    
    Args:
        ocr_result: Result from PaddleOCR
    
    Returns:
        list: List of dictionaries with detailed information
    """
    if not ocr_result or not ocr_result[0]:
        return []
    
    detailed_results = []
    for line in ocr_result[0]:
        if line:
            bbox = line[0]  # Bounding box coordinates
            text = line[1][0]  # Recognized text
            confidence = line[1][1]  # Confidence score
            
            detailed_results.append({
                'text': text,
                'confidence': confidence,
                'bbox': bbox
            })
    
    return detailed_results

def draw_results_on_image(image_path, ocr_result, output_path=None):
    """
    Draw OCR results (bounding boxes and text) on the original image
    
    Args:
        image_path (str): Path to the original image
        ocr_result: Result from PaddleOCR
        output_path (str): Path to save the annotated image
    """
    try:
        # Load image
        image = Image.open(image_path)
        draw = ImageDraw.Draw(image)
        
        # Try to use a better font (macOS compatible)
        try:
            # Try macOS system fonts first
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
        except:
            try:
                # Try alternative macOS font
                font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 20)
            except:
                # Fallback to default font
                font = ImageFont.load_default()
        
        if ocr_result and ocr_result[0]:
            for line in ocr_result[0]:
                if line:
                    bbox = line[0]
                    text = line[1][0]
                    confidence = line[1][1]
                    
                    # Draw bounding box
                    box_points = [(int(point[0]), int(point[1])) for point in bbox]
                    draw.polygon(box_points, outline='red', width=2)
                    
                    # Draw text above the bounding box
                    text_position = (int(bbox[0][0]), int(bbox[0][1]) - 25)
                    draw.text(text_position, f"{text} ({confidence:.2f})", 
                             fill='red', font=font)
        
        # Save or show the result
        if output_path:
            image.save(output_path)
            print(f"Annotated image saved to: {output_path}")
        else:
            image.show()
            
    except Exception as e:
        print(f"Error drawing results: {e}")

def process_multiple_images(image_folder, ocr):
    """
    Process multiple images in a folder
    
    Args:
        image_folder (str): Path to folder containing images
        ocr: PaddleOCR instance
    """
    supported_formats = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif')
    
    if not os.path.exists(image_folder):
        print(f"Error: Folder not found at {image_folder}")
        return
    
    image_files = [f for f in os.listdir(image_folder) 
                   if f.lower().endswith(supported_formats)]
    
    if not image_files:
        print("No supported image files found in the folder")
        return
    
    results = {}
    for image_file in image_files:
        image_path = os.path.join(image_folder, image_file)
        print(f"\nProcessing: {image_file}")
        
        result = recognize_from_image_path(ocr, image_path)
        if result:
            texts = extract_text_only(result)
            results[image_file] = texts
            print(f"Recognized text: {' | '.join(texts)}")
        else:
            results[image_file] = []
            print("No text recognized or error occurred")
    
    return results

def main():
    """
    Main function demonstrating various PaddleOCR usage scenarios
    """
    print("PaddleOCR 3.1 Text Recognition Demo")
    print("=" * 40)
    
    # Initialize PaddleOCR
    print("Initializing PaddleOCR...")
    ocr = setup_paddleocr(use_angle_cls=True, lang='en')
    print("PaddleOCR initialized successfully!")
    
    # Example 1: Process a single image from file path
    print("\n1. Processing single image from file path:")
    image_path = "test.png"  # Replace with your image path
    
    # Create a sample image if it doesn't exist (for demo purposes)
    if not os.path.exists(image_path):
        print(f"Creating sample image at {image_path}")
        sample_img = np.ones((200, 600, 3), dtype=np.uint8) * 255
        cv2.putText(sample_img, "Hello PaddleOCR!", (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
        cv2.imwrite(image_path, sample_img)
    
    result = recognize_from_image_path(ocr, image_path)
    if result:
        # Extract just the text
        texts = extract_text_only(result)
        print(f"Recognized text: {texts}")
        
        # Get detailed results
        detailed = get_detailed_results(result)
        print("\nDetailed results:")
        for item in detailed:
            print(f"Text: '{item['text']}', Confidence: {item['confidence']:.3f}")
        
        # Draw results on image
        draw_results_on_image(image_path, result, "annotated_result.jpg")
    
    # Example 2: Process image from numpy array (OpenCV)
    print("\n2. Processing image from numpy array:")
    img_array = cv2.imread(image_path) if os.path.exists(image_path) else None
    if img_array is not None:
        result = recognize_from_numpy_array(ocr, img_array)
        if result:
            texts = extract_text_only(result)
            print(f"Recognized text from array: {texts}")
    
    # Example 3: Different language support
    print("\n3. Chinese text recognition example:")
    ocr_chinese = setup_paddleocr(use_angle_cls=True, lang='ch')
    # You would use ocr_chinese.ocr() for Chinese text images
    print("Chinese OCR model loaded successfully!")
    
    # Example 4: Batch processing (if you have a folder of images)
    print("\n4. Batch processing example:")
    # Uncomment the following lines if you have a folder of images
    # results = process_multiple_images("./images", ocr)
    # if results:
    #     print(f"Processed {len(results)} images")
    
    # Example 5: Save results to JSON
    print("\n5. Saving results to JSON:")
    if 'result' in locals() and result:
        detailed_results = get_detailed_results(result)
        with open('ocr_results.json', 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, ensure_ascii=False, indent=2)
        print("Results saved to ocr_results.json")
    
    print("\nDemo completed!")

if __name__ == "__main__":
    # Installation for macOS:
    # pip install paddlepaddle
    # pip install paddleocr
    
    # Optional dependencies for better functionality:
    # pip install opencv-python pillow
    
    print("Running on macOS - GPU acceleration disabled for compatibility")
    main()