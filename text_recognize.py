# -*- coding: utf-8 -*-
"""text_recognize.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1EozgVDd83t9DvD1DklRZoNfIHHh4QXU3
"""

!pip install -q paddlepaddle
!pip install -q paddleocr

# Initialize PaddleOCR instance
from paddleocr import PaddleOCR
ocr = PaddleOCR(
    use_doc_orientation_classify=False,
    use_doc_unwarping=False,
    use_textline_orientation=False)

# Run OCR inference on a sample image
result = ocr.predict(
    input="text2.png")

# Visualize the results and save the JSON results
for res in result:
    res.save_to_img("output")
    res.save_to_json("output")

for res in result:
  for text in res.json['res']['rec_texts']:
    print(text)